import 'package:bibl/controllers/heart_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/services/reward_service.dart';
import 'package:bibl/utils/performance_utils.dart';
import 'package:bibl/widgets/achivement_dialog.dart';
import 'package:bibl/widgets/premium_shimmer.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'controllers/lesson_controller.dart';
import 'controllers/quiz_controller.dart';

import 'widgets/merged_items_list_widget.dart';

class Home extends StatefulWidget {
  final ScrollController scrollController;
  const Home({super.key, required this.scrollController});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final ProfileController profileController = Get.find();
  final HeartController heartController = Get.find();
  final QuizController quizController = Get.find();
  final LessonController lessonController = Get.find();

  String weekDaysSelected = 'Pon';
  int streak = 0;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  bool _isInitialized = false;
  bool _isLoadingContent = true;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // Initialize fade animation
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    );
    // Defer initialization to not block UI
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _initializeHome();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _initializeHome() async {
    await PerformanceUtils.measureOperation(
      'Home Initialization',
      () async {
        // Check if data is ready
        if (profileController.userr.value.uid != null && !_isInitialized) {
          // Start fade in animation
          _fadeController.forward();

          // Perform streak checks in background
          _performStreakChecks();

          // Mark as initialized
          setState(() {
            _isInitialized = true;
            _isLoadingContent = false;
          });
        }
      },
    );
  }

  /// Perform streak checks without affecting loading state
  Future<void> _performStreakChecks() async {
    try {
      // Perform both streak checks in parallel
      await Future.wait([
        checkWeeklyStreak(),
        checkConsecutiveStreak(),
      ]);
    } catch (e) {
      // Handle any errors gracefully
      debugPrint('Error during streak checks: $e');
    }
  }

  Future<void> checkWeeklyStreak() async {
    DateTime now = DateTime.now();
    // Check if it's a new day based on 22:00 to 6:00 logic
    DateTime startOfNewDay = DateTime(now.year, now.month, now.day, 6);
    DateTime endOfNewDay = DateTime(now.year, now.month, now.day, 22);

    if (now.isAfter(startOfNewDay) || now.isBefore(endOfNewDay)) {
      // Fetch last streak update date (default to 1 day before today)
      DateTime lastUpdateDate =
          profileController.userr.value.lastStreakUpdate?.toDate() ??
              now.subtract(const Duration(days: 1));

      // Check if the streak was already updated today
      if (now.difference(lastUpdateDate).inDays == 0) {
        return;
      }

      // Initialize reward service
      RewardService rewardService =
          RewardService(profileController.userr.value.uid ?? '');
    }
  }

  Future<void> checkConsecutiveStreak() async {
    // Implementation for consecutive streak checking
    try {
      debugPrint('Checking consecutive streak...');
    } catch (e) {
      debugPrint('Error checking consecutive streak: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      body: Column(
        children: [
          _buildHomeHeader(),
          Expanded(
            child: MergedItemsList(
              isForLibrary: false,
              scrollController: ScrollController(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHomeHeader() {
    return Container(
      height: 175,
      decoration: BoxDecoration(
        gradient: mainColorsGradient,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(18),
          bottomRight: Radius.circular(18),
        ),
        boxShadow: const [
          BoxShadow(
            color: Colors.black26,
            offset: Offset(0, 4),
            blurRadius: 10,
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 20, 16, 5),
          child: Column(
            children: [
              const Spacer(flex: 4),
              _buildHeaderRow(),
              const Spacer(flex: 3),
              _buildDaysWidget(),
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderRow() {
    return Obx(() => Row(
          children: [
            // Logo/App name
            const Text(
              'UmniLab',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            // User stats icons
            _buildStatIcon(
              icon: Icons.favorite,
              value: '${profileController.userr.value.hearts ?? 0}',
            ),
            const SizedBox(width: 10),
            _buildStatIcon(
              icon: Icons.psychology,
              value: '${profileController.userr.value.neurons ?? 0}',
            ),
            const SizedBox(width: 10),
            _buildStatIcon(
              icon: Icons.emoji_events,
              value: '${profileController.userr.value.weeklyStreak ?? 0}',
            ),
          ],
        ));
  }

  Widget _buildStatIcon({required IconData icon, required String value}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white, size: 16),
          const SizedBox(width: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDaysWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: List.generate(7, (index) {
        final days = ['Pon', 'Uto', 'Sre', 'Čet', 'Pet', 'Sub', 'Ned'];
        final isSelected = weekDaysSelected == days[index];

        return Column(
          children: [
            Container(
              height: 18,
              width: 18,
              decoration: BoxDecoration(
                color: isSelected ? Colors.white : Colors.transparent,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.5),
                  width: 1,
                ),
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      color: mainColor,
                      size: 12,
                    )
                  : null,
            ),
            const SizedBox(height: 8),
            Text(
              days[index],
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        );
      }),
    );
  }
}
