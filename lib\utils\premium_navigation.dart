import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Enhanced navigation with premium transitions
class PremiumNavigation {
  // Premium transition duration
  static const Duration transitionDuration = Duration(milliseconds: 350);
  
  // Navigate with custom transition
  static Future<T?> to<T>(
    Widget page, {
    Transition transition = Transition.fadeIn,
    Duration? duration,
    dynamic arguments,
    bool preventDuplicates = true,
    bool? popGesture,
    Curve curve = Curves.easeInOutCubic,
  }) {
    return Get.to<T>(
      () => page,
      transition: transition,
      duration: duration ?? transitionDuration,
      arguments: arguments,
      preventDuplicates: preventDuplicates,
      popGesture: popGesture ?? GetPlatform.isIOS,
      curve: curve,
    );
  }
  
  // Replace current page
  static Future<T?> off<T>(
    Widget page, {
    Transition transition = Transition.fadeIn,
    Duration? duration,
    dynamic arguments,
    bool preventDuplicates = true,
    bool? popGesture,
    Curve curve = Curves.easeInOutCubic,
  }) {
    return Get.off<T>(
      () => page,
      transition: transition,
      duration: duration ?? transitionDuration,
      arguments: arguments,
      preventDuplicates: preventDuplicates,
      popGesture: popGesture ?? GetPlatform.isIOS,
      curve: curve,
    );
  }
  
  // Replace all pages
  static Future<T?> offAll<T>(
    Widget page, {
    Transition transition = Transition.fadeIn,
    Duration? duration,
    dynamic arguments,
    bool? popGesture,
    Curve curve = Curves.easeInOutCubic,
  }) {
    return Get.offAll<T>(
      () => page,
      transition: transition,
      duration: duration ?? transitionDuration,
      arguments: arguments,
      popGesture: popGesture ?? GetPlatform.isIOS,
      curve: curve,
    );
  }
  
  // Custom page transitions
  static GetPageRoute<T> customPageRoute<T>(
    Widget page, {
    RouteSettings? settings,
    bool maintainState = true,
    bool fullscreenDialog = false,
    bool allowSnapshotting = true,
  }) {
    return GetPageRoute<T>(
      page: () => page,
      settings: settings,
      maintainState: maintainState,
      fullscreenDialog: fullscreenDialog,
      allowSnapshotting: allowSnapshotting,
      transition: Transition.fadeIn,
      transitionDuration: transitionDuration,
      curve: Curves.easeInOutCubic,
      customTransition: PremiumTransition(),
    );
  }
}

/// Custom premium transition
class PremiumTransition extends CustomTransition {
  @override
  Widget buildTransition(
    BuildContext context,
    Curve? curve,
    Alignment? alignment,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return Stack(
      children: [
        // Fade transition
        FadeTransition(
          opacity: animation,
          child: child,
        ),
        
        // Scale transition
        ScaleTransition(
          scale: Tween<double>(
            begin: 0.95,
            end: 1.0,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: curve ?? Curves.easeInOutCubic,
          )),
          child: child,
        ),
        
        // Slide transition (subtle)
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, 0.02),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: curve ?? Curves.easeInOutCubic,
          )),
          child: child,
        ),
      ],
    );
  }
}

/// Hero animation wrapper for smooth transitions
class PremiumHero extends StatelessWidget {
  final String tag;
  final Widget child;
  final CreateRectTween? createRectTween;
  final HeroFlightShuttleBuilder? flightShuttleBuilder;
  
  const PremiumHero({
    Key? key,
    required this.tag,
    required this.child,
    this.createRectTween,
    this.flightShuttleBuilder,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: tag,
      createRectTween: createRectTween ?? _defaultCreateRectTween,
      flightShuttleBuilder: flightShuttleBuilder ?? _defaultFlightShuttleBuilder,
      child: Material(
        type: MaterialType.transparency,
        child: child,
      ),
    );
  }
  
  static CreateRectTween _defaultCreateRectTween = (begin, end) {
    return MaterialRectCenterArcTween(begin: begin, end: end);
  };
  
  static Widget _defaultFlightShuttleBuilder(
    BuildContext flightContext,
    Animation<double> animation,
    HeroFlightDirection flightDirection,
    BuildContext fromHeroContext,
    BuildContext toHeroContext,
  ) {
    final Hero toHero = toHeroContext.widget as Hero;
    return FadeTransition(
      opacity: animation.drive(
        Tween<double>(begin: 0.0, end: 1.0).chain(
          CurveTween(curve: Curves.easeInOut),
        ),
      ),
      child: toHero.child,
    );
  }
}
